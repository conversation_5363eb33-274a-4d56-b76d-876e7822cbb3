"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaSearch, FaFilter, FaCog } from 'react-icons/fa';
import { authUtils } from '@/utils/auth.utils';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { userService, type User, type PaginationInfo } from '@/services/user.service';
import UserManageModal from '@/components/users/UserManageModal';
import { useTranslations } from 'next-intl';

export default function UsersSearchPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [isBanned, setIsBanned] = useState<boolean | undefined>(undefined);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    totalPages: 1,
    limit: 10
  });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [, setShowManageModal] = useState(false);

  const currentUser = authUtils.getUserData() as { role: string } | null;

  const t = useTranslations('userSearchPage');

  const getAvailableRoles = () => {
    switch (currentUser?.role) {
      case 'superowner':
        return ['owner','superadmin','admin', 'cashier', 'player'];
      case 'owner':
        return ['superadmin','admin', 'cashier', 'player'];
      case 'superadmin':
        return ['admin', 'cashier', 'player'];
      case 'admin':
        return [ 'cashier', 'player'];
      case 'cashier':
        return [ 'cashier'];
      default:
        return [];
    }
  };

  const fetchUsers = useCallback(async (username?: string, page: number = 1) => {
    setIsLoading(true);
    try {
      const response = await userService.searchUsers({
        username,
        page,
        limit: pagination.limit,
        roles: selectedRoles.length > 0 ? selectedRoles.join(',') : undefined,
        isBanned,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        includeDescendants: selectedRoles.length === 0 // Only include descendants if no roles are selected
      });

      setUsers(response.data.users);
      setPagination(response.data.pagination);
    } catch (error: unknown) {
      console.error('Error fetching users:', error);
      const message = error instanceof Error ? error.message : 'Failed to fetch users';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit, selectedRoles, isBanned, startDate, endDate]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers, selectedRoles, isBanned, startDate, endDate, pagination.limit]);

  const handleSearch = () => {
    if (searchTerm.length >= 4 || searchTerm.length === 0) {
      fetchUsers(searchTerm);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatDate = (dateString: string | undefined) => {
    return format(new Date(dateString || new Date().toISOString()), 'dd/MM/yyyy HH:mm');
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('usersManagement')}</h1>
        <p className="text-gray-600">{t('searchAndManage')}</p>
      </div>

      {/* Search and Filter Section */}

        <div className="flex items-center justify-between">
          <div className="relative flex-1 mr-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t('searchUsersPlaceholder')}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
          <button
            onClick={handleSearch}
            className="btn-primary flex items-center space-x-2 px-4 py-2 mr-4"
          >
            <FaSearch className="w-4 h-4" />
            <span>{t('search')}</span>
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FaFilter className="mr-2" />
            {showFilters ? t('hideFilters') : t('showFilters')}
          </button>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
          className="mt-4"
        >
          <div className="dashboard-card p-4 border border-transparent hover:border-gray-300 transition-all duration-200 ease-in-out">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 ">
                {/* Role Filter */}
                <div className="space-y-2">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">{t('roleFilter')}</h3>
                  <div className="space-y-1">
                    {getAvailableRoles().map((role) => (
                      <label key={role} className="flex items-center space-x-2 hover-lift cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedRoles.includes(role)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRoles([...selectedRoles, role]);
                            } else {
                              setSelectedRoles(selectedRoles.filter(r => r !== role));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="text-sm text-gray-700">{role.charAt(0).toUpperCase() + role.slice(1)}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">{t('statusFilter')}</h3>
                  <div className="space-y-1">
                    <label className="flex items-center space-x-2 hover-lift cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value=""
                        checked={isBanned === undefined}
                        onChange={() => setIsBanned(undefined)}
                        className="text-blue-600 border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="text-sm text-gray-700">{t('allUsers')}</span>
                    </label>
                    <label className="flex items-center space-x-2 hover-lift cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value="false"
                        checked={isBanned === false}
                        onChange={() => setIsBanned(false)}
                        className="text-blue-600 border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="text-sm text-gray-700">{t('activeUsers')}</span>
                    </label>
                    <label className="flex items-center space-x-2 hover-lift cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value="true"
                        checked={isBanned === true}
                        onChange={() => setIsBanned(true)}
                        className="text-blue-600 border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="text-sm text-gray-700">{t('bannedUsers')}</span>
                    </label>
                  </div>
                </div>

                {/* Date Range */}
                <div className="space-y-2">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">{t('dateRange')}</h3>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">{t('startDate')}</label>
                      <input
                        type="date"
                        value={startDate ? format(new Date(startDate), 'yyyy-MM-dd') : ''}
                        onChange={(e) => {
                          const selectedDate = e.target.value;
                          const startDate = selectedDate ? `${selectedDate}T00:01` : '';
                          setStartDate(startDate);
                        }}
                        className="block w-full px-2 py-1 text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">{t('endDate')}</label>
                      <input
                        type="date"
                        value={endDate ? format(new Date(endDate), 'yyyy-MM-dd') : ''}
                        onChange={(e) => {
                          const selectedDate = e.target.value;
                          const endDate = selectedDate ? `${selectedDate}T23:59` : '';
                          setEndDate(endDate);
                        }}
                        className="block w-full px-2 py-1 text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <button
                        onClick={() => {
                          setStartDate('');
                          setEndDate('');
                        }}
                        className="btn-primary px-3 py-1.5 text-sm w-full"
                        disabled={!startDate && !endDate}
                      >
                        {t('clear')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}


      {/* Users Table */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden mt-4">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('username')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('role')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('balance')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('createdAt')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {user.username}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                      user.role === 'superadmin' ? 'bg-blue-500 text-white' :
                      user.role === 'admin' ? 'bg-yellow-500 text-black' :
                      user.role === 'cashier' ? 'bg-green-500 text-white' :
                      user.role === 'player' ? 'bg-gray-500 text-white' :
                      'bg-gray-500 text-white'
                    }`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.balance} {user.currency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {user.is_banned ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                        {t('banned')}
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        {t('active')}
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(user.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => {
                        setSelectedUserId(user.id);
                        setShowManageModal(true);
                      }}
                      className="text-blue-600 hover:text-blue-900 focus:outline-none focus:underline flex items-center justify-end space-x-1"
                    >
                      <FaCog className="w-4 h-4" />
                      <span>{t('manage')}</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {users.length === 0 && !isLoading && (
          <div className="text-center py-8 text-gray-500">
            {t('noUsersFound')}
          </div>
        )}

        {isLoading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="mt-4 flex items-center justify-center">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                const newPage = pagination.page - 1;
                if (newPage >= 1) {
                  fetchUsers(searchTerm, newPage);
                }
              }}
              disabled={pagination.page <= 1}
              className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                pagination.page <= 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              {t('previous')}
            </button>
            <span className="text-sm text-gray-700">
              {t('showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('to')}
              {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('results')}
            </span>
            <button
              onClick={() => {
                const newPage = pagination.page + 1;
                if (newPage <= pagination.totalPages) {
                  fetchUsers(searchTerm, newPage);
                }
              }}
              disabled={pagination.page >= pagination.totalPages}
              className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                pagination.page >= pagination.totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              {t('next')}
            </button>
          </div>
        </div>
      )}

      {/* User Management Modal */}
      {selectedUserId && (
        <UserManageModal
          key={selectedUserId} // Force remount when userId changes
          isOpen={!!selectedUserId}
          onClose={() => {
            setSelectedUserId(null);
          }}
          userId={selectedUserId}
          onUpdate={async () => {
            // This will maintain all current filters and pagination
            await fetchUsers(
              searchTerm.length >= 4 ? searchTerm : undefined,
              pagination.page
            );
          }}
          onManageUser={(userId) => {
            setSelectedUserId(userId);
          }}
        />
      )}
    </div>
  );
}

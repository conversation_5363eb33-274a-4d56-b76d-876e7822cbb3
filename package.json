{"name": "admin-dashboard-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--inspect&& next dev", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@tanstack/react-query": "^5.80.1", "@tanstack/react-query-devtools": "^5.80.1", "@tanstack/react-table": "^8.20.6", "@types/crypto-js": "^4.2.2", "axios": "^1.8.4", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^11.14.4", "next": "^15.2.4", "next-intl": "^3.26.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-icons": "^5.4.0", "sonner": "^1.7.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "depcheck": "^1.4.7", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
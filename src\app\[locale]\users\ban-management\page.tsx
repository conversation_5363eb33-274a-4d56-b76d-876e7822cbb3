"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaBan, FaSearch } from 'react-icons/fa';
import { format } from 'date-fns';
import { toast } from 'sonner';
import BanManagementModal from '@/components/modals/BanManagementModal';
import { userService, type User, type PaginationInfo } from '@/services/user.service';
import { useTranslations } from 'next-intl';

export default function BanManagementPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    totalPages: 1,
    limit: 10
  });

  const t = useTranslations('userBanManagement');

  const fetchUsers = useCallback(async (username?: string, page: number = 1) => {
    try {
      const response = await userService.searchUsers({
        username,
        page,
        limit: pagination.limit,
        level:2,
      });

      setUsers(response.data.users);
      setPagination(response.data.pagination);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Failed to fetch users';
      toast.error(message);
    }
  }, [pagination.limit]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleSearch = () => {
    if (searchTerm.length >= 4 || searchTerm.length === 0) {
      fetchUsers(searchTerm);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatDate = (dateString: string | undefined) => {
    return format(new Date(dateString || new Date().toISOString()), 'dd/MM/yyyy HH:mm');
  };

  const handleBanAction = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('banManagement')}</h1>
        <p className="text-gray-600">{t('manageUserAccess')}</p>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-3">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder={t('searchUsers')}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </div>
        <button
          onClick={handleSearch}
          className="btn-primary flex items-center space-x-2 px-4 py-2"
        >
          <FaSearch className="w-4 h-4" />
          <span>{t('search')}</span>
        </button>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('username')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('role')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('balance')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('createdAt')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user, index) => (
                <motion.tr
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={user.is_banned ? 'bg-red-50' : ''}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{user.username}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${user.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'superadmin' ? 'bg-blue-500 text-white' :
                        user.role === 'admin' ? 'bg-yellow-500 text-black' :
                        user.role === 'cashier' ? 'bg-green-500 text-white' :
                        user.role === 'player' ? 'bg-gray-500 text-white' :
                        'bg-gray-500 text-white'}`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.balance} {user.currency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.is_banned
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {user.is_banned ? t('banned') : t('active')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(user.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleBanAction(user)}
                      className={`inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${
                        user.is_banned
                          ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                          : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                      } focus:outline-none focus:ring-2 focus:ring-offset-2 w-24`}
                    >
                      <FaBan className="mr-1" />
                      {user.is_banned ? t('unban') : t('ban')}
                    </motion.button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="mt-4 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  const newPage = pagination.page - 1;
                  if (newPage >= 1) {
                    fetchUsers(searchTerm, newPage);
                  }
                }}
                disabled={pagination.page <= 1}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  pagination.page <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {t('previous')}
              </button>
              <span className="text-sm text-gray-700">
                {t('showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('to')}
                {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('results')}
              </span>
              <button
                onClick={() => {
                  const newPage = pagination.page + 1;
                  if (newPage <= pagination.totalPages) {
                    fetchUsers(searchTerm, newPage);
                  }
                }}
                disabled={pagination.page >= pagination.totalPages}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  pagination.page >= pagination.totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {t('next')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Ban Management Modal */}
      {selectedUser && (
        <BanManagementModal
          isOpen={isModalOpen}
          onClose={closeModal}
          user={selectedUser}
          onStatusUpdate={() => fetchUsers(searchTerm, pagination.page)}
        />
      )}
    </div>
  );
}

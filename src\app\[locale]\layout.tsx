import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';
import { locales } from '@/i18n/routing';
import '../globals.css';
import { AuthProvider } from '@/components/AuthProvider';
import { TokenRefreshManager } from '@/components/TokenRefreshManager';
import { QueryProvider } from '@/components/QueryProvider';
import { Toaster } from 'sonner';


export const metadata = {
  title: 'iBetX - Admin Dashboard',
  description: 'Admin Dashboard for managing users and transactions',
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: ReactNode;
  params: { locale: typeof locales[number] };
}) {
  const { locale } = await params;

  // Validate the locale
  if (!locales.includes(locale)) notFound();

  // Provide all messages to the client
  const messages = await getMessages({ locale });

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          <QueryProvider>
            <AuthProvider>
              <TokenRefreshManager />
              {children}
              <Toaster position="top-right" richColors expand={false} />
            </AuthProvider>
          </QueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

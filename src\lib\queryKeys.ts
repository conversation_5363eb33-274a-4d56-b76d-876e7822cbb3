import { GameSearchFilters } from '@/services/game.service';

export const queryKeys = {
  games: {
    all: ['games'] as const,
    lists: () => [...queryKeys.games.all, 'list'] as const,
    list: (filters: GameSearchFilters) => [...queryKeys.games.lists(), filters] as const,
    providers: () => [...queryKeys.games.all, 'providers'] as const,
    categories: () => [...queryKeys.games.all, 'categories'] as const,
  },
} as const;

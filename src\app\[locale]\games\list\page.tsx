"use client";

import { useState, useRef, useMemo } from 'react';
import Image from 'next/image';
import { useUserDetails } from '@/hooks/useUserDetails';
import { useRouter } from 'next/navigation';
import { FaGamepad, FaDatabase, FaSearch, FaFilter } from 'react-icons/fa';
import { useGamesQuery, useProvidersQuery, useCategoriesQuery, useInitGamesMutation } from '@/hooks/useGamesQuery';
import Layout from "@/components/layout/Layout";
import InitializeDatabaseModal from "@/components/modals/InitializeDatabaseModal";
import { useTranslations } from 'next-intl';


function GamesListContent() {
  const t = useTranslations('Game-list');
  const router = useRouter();
  const { userDetails } = useUserDetails();

  const formatText = (text: string): string => {
    return text
      .split('_') // Split by underscore
      .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
      .join(' '); // Join with a space
  };

  // Local state for UI interactions
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [tempProvider, setTempProvider] = useState<string>('');
  const [tempCategory, setTempCategory] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const modalRef = useRef<HTMLImageElement>(null);

  // Create filters object for React Query
  const filters = useMemo(() => ({
    page: currentPage,
    limit: 10,
    name: searchTerm.length >= 3 ? searchTerm : undefined,
    title: selectedProvider || undefined,
    categories: selectedCategory || undefined,
  }), [currentPage, searchTerm, selectedProvider, selectedCategory]);

  // React Query hooks
  const { data: gamesData, isLoading: isGamesLoading, error: gamesError } = useGamesQuery(filters);
  const { data: providers = [], isLoading: isProvidersLoading } = useProvidersQuery();
  const { data: categories = [], isLoading: isCategoriesLoading } = useCategoriesQuery();
  const initGamesMutation = useInitGamesMutation();

  // Reset page when filters change
  const handleFilterChange = (newProvider: string, newCategory: string) => {
    setSelectedProvider(newProvider);
    setSelectedCategory(newCategory);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Reset page when search term changes
  const handleSearchChange = (newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when search is triggered
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Check user permissions
  if (userDetails && userDetails.role !== 'superowner') {
    router.push('/');
    return null;
  }

  const handleInitializeClick = () => {
    setIsModalOpen(true);
  };

  const handleInitializeComplete = async () => {
    try {
      await initGamesMutation.mutateAsync();
      setIsModalOpen(false);
    } catch (error) {
      // Error handling is done in the mutation
      throw error;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('gamesManagement')}</h1>
        <p className="text-gray-600">{t('manageAndView')}</p>
      </div>

      {/* Initialization Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <FaDatabase className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className="text-lg font-semibold">{t('gamesDatabase')}</h2>
              <p className="text-sm text-gray-500">{t('initializeGamesDatabase')}</p>
            </div>
          </div>
          <button
            onClick={handleInitializeClick}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {t('initializeDatabase')}
          </button>
    </div>

    <InitializeDatabaseModal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      onInitialize={handleInitializeComplete}
    />
        </div>

        {/* Games List Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center space-x-4 mb-6">
          <FaGamepad className="w-6 h-6 text-green-500" />
          <div>
            <h2 className="text-lg font-semibold">{t('gamesList')}</h2>
            <p className="text-sm text-gray-500">{t('viewAndManage')}</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
            {/* Search Input */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={t('searchGames')}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>

            {/* Search Button */}
            <button
              onClick={handleSearch}
              disabled={isGamesLoading}
              className="btn-primary flex items-center space-x-2 px-4 py-2"
            >
              <FaSearch className="w-4 h-4" />
              <span>{t('search')}</span>
            </button>

            {/* Provider Filter */}
            <div className="custom-select-wrapper">
              <select
                value={tempProvider}
                onChange={(e) => setTempProvider(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleFilterChange(tempProvider, selectedCategory);
                  }
                }}
                onBlur={() => setTempProvider(selectedProvider)}
                onClick={(e) => {
                  const target = e.target as HTMLSelectElement;
                  handleFilterChange(target.value, selectedCategory);
                  setTempProvider(target.value);
                }}
                className="custom-select"
              >
                <option value="">{t('allProviders')}</option>
                {providers.map(provider => (
                  <option key={provider} value={provider}>
                    {formatText(provider)}
                  </option>
                ))}
              </select>
              <div className="custom-select-arrow">
                <FaFilter className="h-5 w-5" />
              </div>
            </div>

            {/* Category Filter */}
            <div className="custom-select-wrapper">
              <select
                value={tempCategory}
                onChange={(e) => setTempCategory(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleFilterChange(selectedProvider, tempCategory);
                  }
                }}
                onBlur={() => setTempCategory(selectedCategory)}
                onClick={(e) => {
                  const target = e.target as HTMLSelectElement;
                  handleFilterChange(selectedProvider, target.value);
                  setTempCategory(target.value);
                }}
                className="custom-select"
              >
                <option value="">{t('allCategories')}</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {formatText(category)}
                  </option>
                ))}
              </select>
              <div className="custom-select-arrow">
                <FaFilter className="h-5 w-5" />
              </div>
            </div>
          </div>
        </div>

        {/* Empty Table Card */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('id')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('name')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('provider')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('image')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('categories')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isGamesLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    {t('loadingGames')}
                  </td>
                </tr>
              ) : !gamesData?.games || gamesData.games.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    {t('noGamesAvailable')}
                  </td>
                </tr>
              ) : (
                gamesData.games.map((game) => (
                  <tr key={game.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {game.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {game.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {game.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap relative">
                      <div
                        className="relative group cursor-pointer"
                        onMouseEnter={() => {
                          const timeout = setTimeout(() => {
                            setHoveredImage(game.img);
                          }, 200);
                          setHoverTimeout(timeout);
                        }}
                        onMouseLeave={() => {
                          if (hoverTimeout) {
                            clearTimeout(hoverTimeout);
                            setHoverTimeout(null);
                          }
                          setHoveredImage(null);
                        }}
                      >
                        <Image
                          src={game.img}
                          alt={game.title}
                          width={40}
                          height={40}
                          className="rounded-full object-cover"
                          style={{ width: 'auto', height: 'auto' }}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {game.categories || ''}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {gamesData?.pagination && gamesData.pagination.totalPages > 1 && (
          <div className="mt-4 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  const newPage = currentPage - 1;
                  if (newPage >= 1) {
                    setCurrentPage(newPage);
                  }
                }}
                disabled={currentPage <= 1}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  currentPage <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                 {t('previous')}
              </button>
              <span className="text-sm text-gray-700">
                {t('showing')} {((currentPage - 1) * gamesData.pagination.limit) + 1} {t('to')}{' '}
                {Math.min(currentPage * gamesData.pagination.limit, gamesData.pagination.total)} {t('of')} {gamesData.pagination.total} {t('results')}
              </span>
              <button
                onClick={() => {
                  const newPage = currentPage + 1;
                  if (newPage <= gamesData.pagination.totalPages) {
                    setCurrentPage(newPage);
                  }
                }}
                disabled={currentPage >= gamesData.pagination.totalPages}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  currentPage >= gamesData.pagination.totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {t('next')}
              </button>
            </div>
          </div>
        )}
        {/* Image Modal */}
        {hoveredImage && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-modal"
            onMouseEnter={() => {

              if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                setHoverTimeout(null);
              }
              setHoveredImage(hoveredImage);
            }}
            onClick={(e) => {
              if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
                setHoveredImage(null);
                if (hoverTimeout) {
                  clearTimeout(hoverTimeout);
                  setHoverTimeout(null);
                }
              }
            }}
            onMouseLeave={() => {
              setHoveredImage(null);
            }}
          >
            <div className="bg-white rounded-lg shadow-xl p-4 max-w-fit">
              <Image
                ref={modalRef}
                src={hoveredImage}
                alt={t('fullSizeGame')}
                width={288}
                height={210}
                className="rounded-md object-contain max-w-full max-h-full"
                style={{ width: 'auto', height: 'auto' }}
              />
              <button
                onClick={() => setHoveredImage(null)}
                className="absolute top-2 right-2 text-gray-600 hover:text-gray-800 focus:outline-none"
              >
                {t('close')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function GamesListPage() {
  return (
    <Layout>
      <GamesListContent />
    </Layout>
  );
}

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameService, GameSearchFilters, PaginatedResponse } from '@/services/game.service';
import { queryKeys } from '@/lib/queryKeys';
import { toast } from 'sonner';

// Hook for fetching games with search and pagination
export function useGamesQuery(filters: GameSearchFilters) {
  return useQuery({
    queryKey: queryKeys.games.list(filters),
    queryFn: () => GameService.searchGames(filters),
    // Enable the query only if we have valid filters
    enabled: true,
    // Keep previous data while fetching new data (for smooth pagination)
    placeholderData: (previousData) => previousData,
  });
}

// Hook for fetching providers
export function useProvidersQuery() {
  return useQuery({
    queryKey: queryKeys.games.providers(),
    queryFn: () => GameService.getProvidersAndCategories('titles'),
    select: (data: string[]) => data.filter(p => p.trim() !== ''),
    // Providers don't change often, so cache for longer
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook for fetching categories
export function useCategoriesQuery() {
  return useQuery({
    queryKey: queryKeys.games.categories(),
    queryFn: () => GameService.getProvidersAndCategories('categories'),
    select: (data: string[]) => data.filter(c => c.trim() !== ''),
    // Categories don't change often, so cache for longer
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook for initializing games database
export function useInitGamesMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: GameService.initGames,
    onSuccess: () => {
      // Invalidate all games queries to refetch fresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.games.all });
      toast.success('Database initialized successfully');
    },
    onError: (error: Error) => {
      console.error('Database initialization error:', error);
      toast.error(error.message || 'Failed to initialize database');
    },
  });
}
